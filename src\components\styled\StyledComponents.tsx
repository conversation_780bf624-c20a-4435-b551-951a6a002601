import { styled } from '@mui/material/styles';
import { Card, Button, Typography, Paper } from '@mui/material';

export const GlassmorphicCard = styled(Card)(({ theme }) => ({
  background: theme.palette.mode === 'dark' 
    ? 'rgba(255, 255, 255, 0.05)' 
    : 'rgba(255, 255, 255, 0.1)',
  backdropFilter: 'blur(20px)',
  border: theme.palette.mode === 'dark'
    ? '1px solid rgba(255, 255, 255, 0.1)'
    : '1px solid rgba(255, 255, 255, 0.2)',
  borderRadius: '20px',
  boxShadow: theme.palette.mode === 'dark'
    ? '0 8px 32px rgba(0, 0, 0, 0.3)'
    : '0 8px 32px rgba(0, 0, 0, 0.1)',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-8px)',
    boxShadow: theme.palette.mode === 'dark'
      ? '0 16px 48px rgba(0, 0, 0, 0.4)'
      : '0 16px 48px rgba(0, 0, 0, 0.15)',
    background: theme.palette.mode === 'dark'
      ? 'rgba(255, 255, 255, 0.08)'
      : 'rgba(255, 255, 255, 0.15)',
  }
}));

export const LuxuryButton = styled(Button)(({ theme }) => ({
  background: `linear-gradient(135deg, ${theme.palette.secondary.main} 0%, ${theme.palette.secondary.dark} 100%)`,
  color: theme.palette.secondary.contrastText,
  padding: '16px 32px',
  borderRadius: '50px',
  fontSize: '1rem',
  fontWeight: 600,
  textTransform: 'none',
  boxShadow: '0 8px 24px rgba(234, 193, 71, 0.3)',
  transition: 'all 0.3s ease',
  '&:hover': {
    background: `linear-gradient(135deg, ${theme.palette.secondary.dark} 0%, ${theme.palette.secondary.main} 100%)`,
    transform: 'translateY(-2px)',
    boxShadow: '0 12px 32px rgba(234, 193, 71, 0.4)',
  }
}));

export const HeroTypography = styled(Typography)(({ theme }) => ({
  fontFamily: '"Playfair Display", serif',
  fontWeight: 700,
  fontSize: 'clamp(2.5rem, 6vw, 4.5rem)',
  lineHeight: 1.1,
  background: theme.palette.mode === 'dark'
    ? `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`
    : `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
  WebkitBackgroundClip: 'text',
  WebkitTextFillColor: 'transparent',
  backgroundClip: 'text',
  marginBottom: theme.spacing(3),
}));

export const ProductCard = styled(Card)(({ theme }) => ({
  borderRadius: '16px',
  overflow: 'hidden',
  transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
  cursor: 'pointer',
  '&:hover': {
    transform: 'translateY(-12px) scale(1.02)',
    boxShadow: theme.shadows[8],
  },
  '& .product-image': {
    transition: 'transform 0.6s ease',
  },
  '&:hover .product-image': {
    transform: 'scale(1.1)',
  }
}));

export const CategoryCard = styled(Paper)(({ theme }) => ({
  position: 'relative',
  borderRadius: '24px',
  overflow: 'hidden',
  cursor: 'pointer',
  transition: 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)',
  backgroundColor: theme.palette.background.paper,
  '&:hover': {
    transform: 'translateY(-16px)',
    boxShadow: theme.shadows[12],
  },
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: theme.palette.mode === 'dark'
      ? 'linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(234, 193, 71, 0.4) 100%)'
      : 'linear-gradient(135deg, rgba(51, 51, 51, 0.7) 0%, rgba(234, 193, 71, 0.3) 100%)',
    opacity: 0,
    transition: 'opacity 0.3s ease',
    zIndex: 1,
  },
  '&:hover::before': {
    opacity: 1,
  }
}));

export const FilterChip = styled(Button)(({ theme }) => ({
  borderRadius: '25px',
  padding: '8px 20px',
  border: `2px solid ${theme.palette.divider}`,
  backgroundColor: 'transparent',
  color: theme.palette.text.primary,
  textTransform: 'none',
  fontWeight: 500,
  transition: 'all 0.3s ease',
  '&:hover': {
    borderColor: theme.palette.secondary.main,
    backgroundColor: theme.palette.secondary.main,
    color: theme.palette.mode === 'dark' ? '#1a1a1a' : theme.palette.secondary.contrastText,
    transform: 'translateY(-2px)',
  },
  '&.active': {
    borderColor: theme.palette.secondary.main,
    backgroundColor: theme.palette.secondary.main,
    color: theme.palette.mode === 'dark' ? '#1a1a1a' : theme.palette.secondary.contrastText,
  }
}));