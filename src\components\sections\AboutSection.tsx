import React from 'react';
import { Container, Typography, Stack, Box, useColorScheme } from '@mui/material';
import { GlassmorphicCard } from '../styled/StyledComponents';

const AboutSection: React.FC = () => {
  const { mode } = useColorScheme();
  const isDark = mode === 'dark';

  return (
    <Box 
      className="py-20 relative overflow-hidden"
      sx={{
        background: isDark 
          ? 'linear-gradient(135deg, #000000 0%, #1a1a1a 50%, #0a0a0a 100%)'
          : 'linear-gradient(135deg, #374151 0%, #1f2937 50%, #000000 100%)'
      }}
    >
      {/* Background Elements */}
      <Box
        className="absolute inset-0 opacity-10"
        sx={{
          backgroundImage: `url('https://images.unsplash.com/photo-1640007689958-d49cef861e4d?crop=entropy&cs=srgb&fm=jpg&ixid=M3w3NTAwNDR8MHwxfHNlYXJjaHwxfHx0cmF2ZXJ0aW5lJTIwbGltZXN0b25lJTIwYmVpZ2UlMjBuYXR1cmFsfGVufDB8Mnx8b3JhbmdlfDE3NTQ0MDc5Nzl8MA&ixlib=rb-4.1.0&q=85')`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
        }}
      />
      
      <Container maxWidth="lg" className="relative z-10">
        <Stack direction={{ xs: 'column', lg: 'row' }} spacing={8} alignItems="center">
          {/* Content */}
          <Box className="flex-1">
            <Typography
              variant="h2"
              className="text-white mb-6"
              sx={{
                fontFamily: '"Playfair Display", serif',
                fontWeight: 600,
                background: 'linear-gradient(135deg, #ffffff 0%, #eac147 100%)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                backgroundClip: 'text',
              }}
            >
              Crafting Excellence
              <br />
              Since 1999
            </Typography>

            <Typography
              variant="h6"
              className="text-gray-300 mb-8 leading-relaxed"
              sx={{ fontFamily: '"Inter", sans-serif', fontWeight: 300 }}
            >
              For over two decades, we have been at the forefront of luxury stone and ceramic sourcing, 
              bringing the world's finest materials to discerning architects, designers, and homeowners.
            </Typography>

            <Stack spacing={6}>
              <Box>
                <Typography
                  variant="h5"
                  className="text-white mb-3 font-semibold"
                  sx={{ fontFamily: '"Playfair Display", serif' }}
                >
                  Our Heritage
                </Typography>
                <Typography
                  variant="body1"
                  className="text-gray-400 leading-relaxed"
                  sx={{ fontFamily: '"Inter", sans-serif' }}
                >
                  Founded with a passion for natural beauty and architectural excellence, our journey began 
                  in the quarries of Italy and has expanded to source the finest materials from around the globe. 
                  Each piece in our collection tells a story of geological wonder and human craftsmanship.
                </Typography>
              </Box>

              <Box>
                <Typography
                  variant="h5"
                  className="text-white mb-3 font-semibold"
                  sx={{ fontFamily: '"Playfair Display", serif' }}
                >
                  Quality Promise
                </Typography>
                <Typography
                  variant="body1"
                  className="text-gray-400 leading-relaxed"
                  sx={{ fontFamily: '"Inter", sans-serif' }}
                >
                  Every stone and ceramic piece undergoes rigorous quality assessment. We work directly 
                  with quarries and manufacturers, ensuring authenticity, durability, and the highest 
                  standards of finish for your most important projects.
                </Typography>
              </Box>
            </Stack>
          </Box>

          {/* Stats Cards */}
          <Box className="flex-1">
            <Stack spacing={4}>
              <GlassmorphicCard className="p-8 text-center">
                <Typography
                  variant="h2"
                  className="text-white font-bold mb-2"
                  sx={{ fontFamily: '"Playfair Display", serif' }}
                >
                  25+
                </Typography>
                <Typography
                  variant="h6"
                  className="text-yellow-400 mb-2"
                  sx={{ fontFamily: '"Inter", sans-serif', fontWeight: 600 }}
                >
                  Years of Excellence
                </Typography>
                <Typography
                  variant="body2"
                  className="text-gray-300"
                  sx={{ fontFamily: '"Inter", sans-serif' }}
                >
                  Serving luxury projects worldwide
                </Typography>
              </GlassmorphicCard>

              <Stack direction="row" spacing={4}>
                <GlassmorphicCard className="flex-1 p-6 text-center">
                  <Typography
                    variant="h3"
                    className="text-white font-bold mb-1"
                    sx={{ fontFamily: '"Playfair Display", serif' }}
                  >
                    500+
                  </Typography>
                  <Typography
                    variant="body2"
                    className="text-yellow-400 font-semibold mb-1"
                  >
                    Premium Products
                  </Typography>
                  <Typography
                    variant="caption"
                    className="text-gray-400"
                  >
                    Curated collection
                  </Typography>
                </GlassmorphicCard>

                <GlassmorphicCard className="flex-1 p-6 text-center">
                  <Typography
                    variant="h3"
                    className="text-white font-bold mb-1"
                    sx={{ fontFamily: '"Playfair Display", serif' }}
                  >
                    1000+
                  </Typography>
                  <Typography
                    variant="body2"
                    className="text-yellow-400 font-semibold mb-1"
                  >
                    Satisfied Clients
                  </Typography>
                  <Typography
                    variant="caption"
                    className="text-gray-400"
                  >
                    Worldwide projects
                  </Typography>
                </GlassmorphicCard>
              </Stack>

              <GlassmorphicCard className="p-8 text-center">
                <Typography
                  variant="h2"
                  className="text-white font-bold mb-2"
                  sx={{ fontFamily: '"Playfair Display", serif' }}
                >
                  15
                </Typography>
                <Typography
                  variant="h6"
                  className="text-yellow-400 mb-2"
                  sx={{ fontFamily: '"Inter", sans-serif', fontWeight: 600 }}
                >
                  Countries Sourced
                </Typography>
                <Typography
                  variant="body2"
                  className="text-gray-300"
                  sx={{ fontFamily: '"Inter", sans-serif' }}
                >
                  From Italian Carrara to Turkish travertine
                </Typography>
              </GlassmorphicCard>
            </Stack>
          </Box>
        </Stack>

        {/* Bottom Section */}
        <Box className="mt-16 text-center">
          <Typography
            variant="h4"
            className="text-white mb-4"
            sx={{
              fontFamily: '"Playfair Display", serif',
              fontWeight: 600,
              fontStyle: 'italic'
            }}
          >
            "Every stone has a story, every project a legacy"
          </Typography>
          <Typography
            variant="body1"
            className="text-gray-400"
            sx={{ fontFamily: '"Inter", sans-serif' }}
          >
            — Our founding philosophy
          </Typography>
        </Box>
      </Container>
    </Box>
  );
};

export default AboutSection;