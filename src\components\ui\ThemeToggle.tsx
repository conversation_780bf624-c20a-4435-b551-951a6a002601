import React from 'react';
import { styled } from '@mui/material/styles';
import { Box, useColorScheme } from '@mui/material';
import { <PERSON>, <PERSON>, Star } from 'lucide-react';

const ToggleContainer = styled(Box)(({ theme }) => ({
  position: 'relative',
  width: '64px',
  height: '32px',
  borderRadius: '16px',
  background: '#4ECDC4',
  cursor: 'pointer',
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  display: 'flex',
  alignItems: 'center',
  padding: '2px',
  boxShadow: 'inset 0 2px 4px rgba(0, 0, 0, 0.1)',
  '&:hover': {
    transform: 'scale(1.05)',
    boxShadow: 'inset 0 2px 4px rgba(0, 0, 0, 0.15), 0 4px 12px rgba(78, 205, 196, 0.3)',
  }
}));

const ToggleCircle = styled(Box)<{ isDark: boolean }>(({ theme, isDark }) => ({
  position: 'absolute',
  width: '28px',
  height: '28px',
  borderRadius: '50%',
  background: '#ffffff',
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  transform: isDark ? 'translateX(32px)' : 'translateX(0px)',
  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.2)',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  zIndex: 2,
}));

const IconContainer = styled(Box)<{ isDark: boolean; position: 'left' | 'right' }>(({ isDark, position }) => ({
  position: 'absolute',
  width: '28px',
  height: '28px',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  left: position === 'left' ? '2px' : 'auto',
  right: position === 'right' ? '2px' : 'auto',
  opacity: (position === 'left' && !isDark) || (position === 'right' && isDark) ? 0.3 : 1,
  transform: (position === 'left' && !isDark) || (position === 'right' && isDark) ? 'scale(0.8)' : 'scale(1)',
  zIndex: 1,
}));

const StarsContainer = styled(Box)({
  position: 'absolute',
  right: '8px',
  top: '50%',
  transform: 'translateY(-50%)',
  display: 'flex',
  alignItems: 'center',
  gap: '2px',
});

const ThemeToggle: React.FC = () => {
  const { mode, setMode } = useColorScheme();
  const isDark = mode === 'dark';

  const handleToggle = () => {
    setMode(isDark ? 'light' : 'dark');
  };

  return (
    <ToggleContainer onClick={handleToggle}>
      {/* Sun Icon - Left Side */}
      <IconContainer isDark={isDark} position="left">
        <Sun 
          size={16} 
          color="#FFD700" 
          fill={!isDark ? "#FFD700" : "transparent"}
          style={{
            filter: !isDark ? 'drop-shadow(0 0 4px rgba(255, 215, 0, 0.6))' : 'none'
          }}
        />
      </IconContainer>

      {/* Moon and Stars - Right Side */}
      <IconContainer isDark={isDark} position="right">
        <Moon 
          size={14} 
          color="#FFD700" 
          fill={isDark ? "#FFD700" : "transparent"}
          style={{
            filter: isDark ? 'drop-shadow(0 0 4px rgba(255, 215, 0, 0.6))' : 'none'
          }}
        />
        <StarsContainer>
          <Star 
            size={6} 
            color="#FFD700" 
            fill={isDark ? "#FFD700" : "transparent"}
            style={{
              opacity: isDark ? 1 : 0.3,
              transition: 'opacity 0.3s ease'
            }}
          />
          <Star 
            size={4} 
            color="#FFD700" 
            fill={isDark ? "#FFD700" : "transparent"}
            style={{
              opacity: isDark ? 0.8 : 0.2,
              transition: 'opacity 0.3s ease',
              marginTop: '-4px'
            }}
          />
        </StarsContainer>
      </IconContainer>

      {/* Moving Circle */}
      <ToggleCircle isDark={isDark} />
    </ToggleContainer>
  );
};

export default ThemeToggle;