import { experimental_extendTheme as extendTheme } from '@mui/material/styles';

const theme = extendTheme({
  defaultColorScheme: 'dark',
  colorSchemes: {
    light: {
      palette: {
        primary: {
          main: '#333333',
          light: '#666666',
          dark: '#1a1a1a',
          contrastText: '#ffffff'
        },
        secondary: {
          main: '#eac147',
          light: '#f0d470',
          dark: '#c19a2e',
          contrastText: '#333333'
        },
        background: {
          default: '#f2f2f2',
          paper: '#ffffff'
        },
        text: {
          primary: '#333333',
          secondary: '#666666',
          disabled: '#999999'
        },
        grey: {
          50: '#fafafa',
          100: '#f5f5f5',
          200: '#eeeeee',
          300: '#e0e0e0',
          400: '#bdbdbd',
          500: '#9e9e9e',
          600: '#757575',
          700: '#616161',
          800: '#424242',
          900: '#212121'
        },
        common: {
          black: '#000000',
          white: '#ffffff'
        },
        divider: '#ded8d3'
      }
    },
    dark: {
      palette: {
        primary: {
          main: '#ffffff',
          light: '#f5f5f5',
          dark: '#e0e0e0',
          contrastText: '#1a1a1a'
        },
        secondary: {
          main: '#eac147',
          light: '#f0d470',
          dark: '#c19a2e',
          contrastText: '#1a1a1a'
        },
        background: {
          default: '#0a0a0a',
          paper: '#1a1a1a'
        },
        text: {
          primary: '#ffffff',
          secondary: '#b3b3b3',
          disabled: '#666666'
        },
        grey: {
          50: '#1a1a1a',
          100: '#2d2d2d',
          200: '#404040',
          300: '#525252',
          400: '#666666',
          500: '#808080',
          600: '#999999',
          700: '#b3b3b3',
          800: '#cccccc',
          900: '#e6e6e6'
        },
        common: {
          black: '#000000',
          white: '#ffffff'
        },
        divider: '#404040'
      }
    }
  },
  typography: {
    fontFamily: '"Playfair Display", "Inter", serif, sans-serif',
    h1: {
      fontFamily: '"Playfair Display", serif',
      fontWeight: 700,
      fontSize: 'clamp(2.5rem, 6vw, 4.5rem)',
      lineHeight: 1.2,
      letterSpacing: '-0.02em'
    },
    h2: {
      fontFamily: '"Playfair Display", serif',
      fontWeight: 600,
      fontSize: 'clamp(2rem, 5vw, 3.5rem)',
      lineHeight: 1.3,
      letterSpacing: '-0.01em'
    },
    h3: {
      fontFamily: '"Playfair Display", serif',
      fontWeight: 600,
      fontSize: 'clamp(1.75rem, 4vw, 2.25rem)',
      lineHeight: 1.3
    },
    h4: {
      fontFamily: '"Playfair Display", serif',
      fontWeight: 500,
      fontSize: 'clamp(1.5rem, 3vw, 1.75rem)',
      lineHeight: 1.4
    },
    h5: {
      fontFamily: '"Inter", sans-serif',
      fontWeight: 600,
      fontSize: '1.25rem',
      lineHeight: 1.4
    },
    h6: {
      fontFamily: '"Inter", sans-serif',
      fontWeight: 600,
      fontSize: '1.125rem',
      lineHeight: 1.4
    },
    body1: {
      fontFamily: '"Inter", sans-serif',
      fontWeight: 400,
      fontSize: '1rem',
      lineHeight: 1.6
    },
    body2: {
      fontFamily: '"Inter", sans-serif',
      fontWeight: 400,
      fontSize: '0.875rem',
      lineHeight: 1.5
    },
    button: {
      fontFamily: '"Inter", sans-serif',
      fontWeight: 500,
      fontSize: '0.875rem',
      textTransform: 'none',
      letterSpacing: '0.02em'
    }
  },
  shape: {
    borderRadius: 12
  },
  shadows: [
    'none',
    '0px 2px 4px rgba(0, 0, 0, 0.1)',
    '0px 4px 8px rgba(0, 0, 0, 0.15)',
    '0px 8px 16px rgba(0, 0, 0, 0.2)',
    '0px 12px 24px rgba(0, 0, 0, 0.25)',
    '0px 16px 32px rgba(0, 0, 0, 0.3)',
    '0px 20px 40px rgba(0, 0, 0, 0.35)',
    '0px 24px 48px rgba(0, 0, 0, 0.4)',
    '0px 28px 56px rgba(0, 0, 0, 0.45)',
    '0px 32px 64px rgba(0, 0, 0, 0.5)',
    '0px 36px 72px rgba(0, 0, 0, 0.55)',
    '0px 40px 80px rgba(0, 0, 0, 0.6)',
    '0px 44px 88px rgba(0, 0, 0, 0.65)',
    '0px 48px 96px rgba(0, 0, 0, 0.7)',
    '0px 52px 104px rgba(0, 0, 0, 0.75)',
    '0px 56px 112px rgba(0, 0, 0, 0.8)',
    '0px 60px 120px rgba(0, 0, 0, 0.85)',
    '0px 64px 128px rgba(0, 0, 0, 0.9)',
    '0px 68px 136px rgba(0, 0, 0, 0.95)',
    '0px 72px 144px rgba(0, 0, 0, 1)',
    '0px 76px 152px rgba(0, 0, 0, 1)',
    '0px 80px 160px rgba(0, 0, 0, 1)',
    '0px 84px 168px rgba(0, 0, 0, 1)',
    '0px 88px 176px rgba(0, 0, 0, 1)',
    '0px 92px 184px rgba(0, 0, 0, 1)'
  ]
});

export default theme;