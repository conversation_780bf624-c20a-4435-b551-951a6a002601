import React, { useState } from 'react';
import { A<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, IconButton, Stack, useMediaQuery, useTheme, useColorScheme } from '@mui/material';
import { Menu, Globe, X } from 'lucide-react';
import { Language } from '../../types/product';
import ThemeToggle from '../ui/ThemeToggle';

interface HeaderProps {
  currentLanguage: Language;
  onLanguageChange: (language: Language) => void;
}

const languages: Language[] = [
  { code: 'en', name: 'English', direction: 'ltr' },
  { code: 'ar', name: 'العربية', direction: 'rtl' }
];

const Header: React.FC<HeaderProps> = ({ currentLanguage, onLanguageChange }) => {
  const theme = useTheme();
  const { mode } = useColorScheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const isDark = mode === 'dark';

  const handleLanguageToggle = () => {
    const nextLanguage = languages.find(lang => lang.code !== currentLanguage.code) || languages[0];
    onLanguageChange(nextLanguage);
  };

  return (
    <AppBar 
      position="fixed" 
      elevation={0}
      sx={{ 
        backgroundColor: isDark ? 'rgba(26, 26, 26, 0.9)' : 'rgba(255, 255, 255, 0.8)',
        backdropFilter: 'blur(20px)',
        borderBottom: isDark ? '1px solid rgba(64, 64, 64, 0.5)' : '1px solid rgba(222, 216, 211, 0.5)',
        transition: 'all 0.3s ease'
      }}
    >
      <Toolbar className="px-4 md:px-8">
        <Typography
          variant="h5"
          component="div"
          className="flex-1 font-bold"
          sx={{ 
            fontFamily: '"Playfair Display", serif',
            color: 'primary.main',
            fontWeight: 700
          }}
        >
          Luxe Stone
        </Typography>

        {!isMobile && (
          <Stack direction="row" spacing={4} className="mr-8">
            <Typography 
              variant="body1" 
              className="cursor-pointer hover:text-yellow-600 transition-colors"
              sx={{ fontWeight: 500 }}
            >
              Home
            </Typography>
            <Typography 
              variant="body1" 
              className="cursor-pointer hover:text-yellow-600 transition-colors"
              sx={{ fontWeight: 500 }}
            >
              Gallery
            </Typography>
            <Typography 
              variant="body1" 
              className="cursor-pointer hover:text-yellow-600 transition-colors"
              sx={{ fontWeight: 500 }}
            >
              About
            </Typography>
            <Typography 
              variant="body1" 
              className="cursor-pointer hover:text-yellow-600 transition-colors"
              sx={{ fontWeight: 500 }}
            >
              Contact
            </Typography>
          </Stack>
        )}

        <Stack direction="row" spacing={2} alignItems="center">
          <ThemeToggle />
          <IconButton
            onClick={handleLanguageToggle}
            sx={{ 
              color: 'primary.main',
              '&:hover': {
                backgroundColor: isDark ? 'rgba(234, 193, 71, 0.1)' : 'rgba(234, 193, 71, 0.1)'
              }
            }}
          >
            <Globe size={24} />
          </IconButton>
        </Stack>

        {isMobile && (
          <IconButton
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            sx={{ color: 'primary.main' }}
          >
            {mobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </IconButton>
        )}
      </Toolbar>

      {/* Mobile Menu */}
      {isMobile && mobileMenuOpen && (
        <div 
          style={{
            backgroundColor: isDark ? 'rgba(26, 26, 26, 0.95)' : 'rgba(255, 255, 255, 0.95)',
            backdropFilter: 'blur(20px)',
            borderTop: isDark ? '1px solid rgba(64, 64, 64, 0.5)' : '1px solid rgba(222, 216, 211, 0.5)',
            padding: '24px 16px'
          }}
        >
          <Stack spacing={3}>
            <Typography 
              variant="body1" 
              sx={{ 
                cursor: 'pointer',
                color: 'text.primary',
                '&:hover': { color: 'secondary.main' },
                transition: 'color 0.3s ease'
              }}
            >
              Home
            </Typography>
            <Typography 
              variant="body1" 
              sx={{ 
                cursor: 'pointer',
                color: 'text.primary',
                '&:hover': { color: 'secondary.main' },
                transition: 'color 0.3s ease'
              }}
            >
              Gallery
            </Typography>
            <Typography 
              variant="body1" 
              sx={{ 
                cursor: 'pointer',
                color: 'text.primary',
                '&:hover': { color: 'secondary.main' },
                transition: 'color 0.3s ease'
              }}
            >
              About
            </Typography>
            <Typography 
              variant="body1" 
              sx={{ 
                cursor: 'pointer',
                color: 'text.primary',
                '&:hover': { color: 'secondary.main' },
                transition: 'color 0.3s ease'
              }}
            >
              Contact
            </Typography>
          </Stack>
        </div>
      )}
    </AppBar>
  );
};

export default Header;