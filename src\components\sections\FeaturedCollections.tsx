import React from 'react';
import { Container, Typography, Stack, Box, useColorScheme } from '@mui/material';
import { CategoryCard } from '../styled/StyledComponents';
import { categoryImages } from '../../data/mockData';
import { ProductCategory } from '../../types/product';

interface FeaturedCollectionsProps {
  onCategorySelect: (category: ProductCategory) => void;
}

const categoryData = [
  {
    category: 'marble' as ProductCategory,
    title: 'Marble Collection',
    description: 'Timeless elegance with natural veining patterns',
    featured: true
  },
  {
    category: 'granite' as ProductCategory,
    title: 'Granite Collection',
    description: 'Durable luxury with stunning crystalline structures',
    featured: true
  },
  {
    category: 'travertine' as ProductCategory,
    title: 'Travertine Collection',
    description: 'Classic Roman-inspired natural limestone',
    featured: false
  },
  {
    category: 'ceramic' as ProductCategory,
    title: 'Ceramic Collection',
    description: 'Contemporary designs for modern spaces',
    featured: false
  }
];

const FeaturedCollections: React.FC<FeaturedCollectionsProps> = ({ onCategorySelect }) => {
  const { mode } = useColorScheme();
  const isDark = mode === 'dark';

  return (
    <Box 
      className="py-20"
      sx={{
        background: isDark 
          ? 'linear-gradient(to bottom, #1a1a1a, #0a0a0a)'
          : 'linear-gradient(to bottom, #f9fafb, #ffffff)'
      }}
    >
      <Container maxWidth="lg">
        <Stack spacing={8}>
          {/* Section Header */}
          <Box className="text-center max-w-3xl mx-auto">
            <Typography
              variant="h2"
              className="mb-6"
              sx={{
                fontFamily: '"Playfair Display", serif',
                fontWeight: 600,
                background: 'linear-gradient(135deg, #333333 0%, #eac147 100%)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                backgroundClip: 'text',
              }}
            >
              Featured Collections
            </Typography>
            <Typography
              variant="h6"
              sx={{ 
                fontFamily: '"Inter", sans-serif', 
                fontWeight: 300,
                color: 'text.secondary',
                lineHeight: 1.6
              }}
            >
              Explore our carefully curated selection of premium natural stones and contemporary ceramics, 
              each piece selected for its exceptional quality and timeless beauty.
            </Typography>
          </Box>

          {/* Collections Grid */}
          <Box className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {categoryData.map((item, index) => (
              <CategoryCard
                key={item.category}
                onClick={() => onCategorySelect(item.category)}
                className={`relative cursor-pointer group ${
                  item.featured ? 'md:col-span-1 lg:row-span-2' : ''
                }`}
                sx={{
                  height: item.featured ? { xs: 300, md: 400, lg: 500 } : { xs: 300, md: 300 },
                  transform: index % 2 === 0 ? 'translateY(0)' : 'translateY(20px)',
                }}
              >
                <Box
                  className="absolute inset-0"
                  sx={{
                    backgroundImage: `url(${categoryImages[item.category]})`,
                    backgroundSize: 'cover',
                    backgroundPosition: 'center',
                    transition: 'transform 0.6s ease',
                  }}
                />
                
                {/* Overlay */}
                <Box
                  className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent 
                           group-hover:from-black/80 group-hover:via-black/30 transition-all duration-500"
                />
                
                {/* Content */}
                <Box className="absolute inset-0 p-6 flex flex-col justify-end">
                  <Typography
                    variant="h5"
                    className="text-white font-semibold mb-2 transform translate-y-2 
                             group-hover:translate-y-0 transition-transform duration-300"
                    sx={{ fontFamily: '"Playfair Display", serif' }}
                  >
                    {item.title}
                  </Typography>
                  <Typography
                    variant="body2"
                    className="text-white/80 opacity-0 group-hover:opacity-100 
                             transform translate-y-4 group-hover:translate-y-0 
                             transition-all duration-300 delay-100"
                    sx={{ fontFamily: '"Inter", sans-serif' }}
                  >
                    {item.description}
                  </Typography>
                  
                  {/* Hover indicator */}
                  <Box
                    className="absolute top-6 right-6 w-12 h-12 rounded-full border-2 border-white/30 
                             flex items-center justify-center opacity-0 group-hover:opacity-100 
                             transform scale-75 group-hover:scale-100 transition-all duration-300"
                  >
                    <Box className="w-6 h-6 rounded-full bg-white/20 backdrop-blur-sm" />
                  </Box>
                </Box>

                {/* Glassmorphic accent */}
                <Box
                  className="absolute top-4 left-4 px-3 py-1 rounded-full opacity-0 
                           group-hover:opacity-100 transition-opacity duration-300 delay-200"
                  sx={{
                    background: 'rgba(255, 255, 255, 0.1)',
                    backdropFilter: 'blur(10px)',
                    border: '1px solid rgba(255, 255, 255, 0.2)',
                  }}
                >
                  <Typography variant="caption" className="text-white font-medium">
                    {item.featured ? 'Featured' : 'Collection'}
                  </Typography>
                </Box>
              </CategoryCard>
            ))}
          </Box>

          {/* Bottom CTA */}
          <Box className="text-center pt-8">
            <Typography
              variant="body1"
              className="text-gray-500 mb-4"
              sx={{ fontFamily: '"Inter", sans-serif' }}
            >
              Discover the perfect stone for your next project
            </Typography>
            <Box
              className="inline-block px-8 py-3 rounded-full border-2 border-yellow-400 
                       text-yellow-600 hover:bg-yellow-400 hover:text-white 
                       transition-all duration-300 cursor-pointer"
            >
              <Typography variant="button" className="font-semibold">
                View All Collections
              </Typography>
            </Box>
          </Box>
        </Stack>
      </Container>
    </Box>
  );
};

export default FeaturedCollections;