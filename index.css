/* Font imports */
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,500;0,600;0,700;1,400&family=Inter:wght@300;400;500;600;700&display=swap');

/* Tailwind CSS */
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  margin: auto;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--mui-palette-background-default);
}

::-webkit-scrollbar-thumb {
  background: var(--mui-palette-secondary-main);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--mui-palette-secondary-dark);
}

/* Dark mode scrollbar */
[data-mui-color-scheme="dark"] ::-webkit-scrollbar-track {
  background: #1a1a1a;
}

[data-mui-color-scheme="dark"] ::-webkit-scrollbar-thumb {
  background: #eac147;
}

[data-mui-color-scheme="dark"] ::-webkit-scrollbar-thumb:hover {
  background: #f0d470;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(5deg);
  }
}

.animate-fadeInUp {
  animation: fadeInUp 0.6s ease forwards;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

/* Glassmorphism utilities */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Text clamp utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* RTL Support */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] .MuiDrawer-paperAnchorLeft {
  right: 0;
  left: auto;
}

[dir="rtl"] .MuiDrawer-paperAnchorRight {
  left: 0;
  right: auto;
}

/* Dark mode specific styles */
[data-mui-color-scheme="dark"] {
  color-scheme: dark;
}

[data-mui-color-scheme="dark"] body {
  background-color: #0a0a0a;
  color: #ffffff;
}

/* Ensure proper contrast in dark mode */
[data-mui-color-scheme="dark"] .glass {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

[data-mui-color-scheme="dark"] .loading-shimmer {
  background: linear-gradient(90deg, #2d2d2d 25%, #404040 50%, #2d2d2d 75%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

/* Focus styles */
.focus-visible:focus {
  outline: 2px solid #eac147;
  outline-offset: 2px;
}

/* Loading states */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}